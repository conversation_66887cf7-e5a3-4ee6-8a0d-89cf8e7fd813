# PostgreSQL Configuration
POSTGRES_PASSWORD=RxfkBTVUN8BqywRXgeTn8ZBIm5c8L/mp

# Keycloak Configuration
KEYCLOAK_HOSTNAME=localhost
KEYCLOAK_ADMIN_USER=admin
K<PERSON>K_ADMIN_PASSWORD=/SSyyToKDXGNMnyCNyrPfx/Wc4xdpnCx

# OAuth Configuration for OpenWebUI
# These will be configured after Keycloak setup
OAUTH_CLIENT_ID=open-webui
OAUTH_CLIENT_SECRET=DJhFOuxnKItSZWUuxV
OAUTH_SERVER_URL=http://localhost:9090/realms/openwebui
OAUTH_REDIRECT_URI=http://localhost:3000/oauth/oidc/callback

# OpenWebUI Configuration
WEBUI_SECRET_KEY=vyfcHS/9YSKQGvZe4vPrVu9FhxlMd9EtQbAdkvkX64caFjacVaL2SE7ZDyCrjFeG

# Ollama Configuration
OLLAMA_BASE_URL=http://ollama:11434
OLLAMA_MODELS_DIR=/root/.ollama/models

# Apache Tika Configuration
TIKA_BASE_URL=http://tika:9998

# Domain Configuration (update these with your actual domains)
# If using local development, keep as localhost
# For production, replace with your actual domain names
DOMAIN_KEYCLOAK=localhost
DOMAIN_OPENWEBUI=localhost
DOMAIN_NPM=localhost
DOMAIN_OLLAMA=localhost:11434
DOMAIN_TIKA=localhost:9998

# SSL Configuration (for production)
# Set to true when using real domains with SSL
USE_SSL=true

# CUDA Configuration
NVIDIA_VISIBLE_DEVICES=all
NVIDIA_DRIVER_CAPABILITIES=compute,utility

# Network Configuration
NETWORK_SUBNET=**********/16
