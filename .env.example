# =============================================================================
# PRODUCTION-READY ENVIRONMENT CONFIGURATION
# =============================================================================
# SECURITY WARNING: Copy this file to .env and update ALL values!
# DO NOT use these example values in production!
# Generate secure random values for all passwords and secrets!
# =============================================================================

# =============================================================================
# DOMAIN AND URL CONFIGURATION
# =============================================================================
DOMAIN=your-domain.com
WEBUI_URL=https://openwebui.your-domain.com
KEYCLOAK_HOSTNAME=keycloak.your-domain.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Main Database (for NPM or OpenWebUI depending on configuration)
POSTGRES_DB=openwebui
POSTGRES_USER=openwebui
POSTGRES_PASSWORD=CHANGE_THIS_TO_SECURE_RANDOM_PASSWORD_32_CHARS

# OpenWebUI Database (when using separate database)
OPENWEBUI_DB_NAME=openwebui
OPENWEBUI_DB_USER=openwebui
OPENWEBUI_DB_PASSWORD=CHANGE_THIS_TO_SECURE_RANDOM_PASSWORD_32_CHARS

# Keycloak Database
KEYCLOAK_DB_NAME=keycloak
KEYCLOAK_DB_USER=keycloak
KEYCLOAK_DB_PASSWORD=CHANGE_THIS_TO_SECURE_RANDOM_PASSWORD_32_CHARS

# =============================================================================
# KEYCLOAK CONFIGURATION
# =============================================================================
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=CHANGE_THIS_TO_SECURE_RANDOM_PASSWORD_32_CHARS
KC_LOG_LEVEL=INFO

# =============================================================================
# OPENWEBUI CORE CONFIGURATION
# =============================================================================
WEBUI_NAME=OpenWebUI Multi-User AI Platform
WEBUI_SECRET_KEY=CHANGE_THIS_TO_SECURE_RANDOM_SECRET_KEY_32_CHARS_MIN
WEBUI_LOG_LEVEL=INFO

# =============================================================================
# OAUTH/OIDC AUTHENTICATION CONFIGURATION
# =============================================================================
OAUTH_CLIENT_ID=openwebui
OAUTH_CLIENT_SECRET=CHANGE_THIS_TO_SECURE_RANDOM_CLIENT_SECRET_32_CHARS
OAUTH_PROVIDER_NAME=Keycloak SSO
OPENID_PROVIDER_URL=https://keycloak.your-domain.com/realms/openwebui-realm
OAUTH_SCOPES=openid profile email
OAUTH_ROLES_CLAIM=openwebui_roles
OAUTH_REDIRECT_URI=https://openwebui.your-domain.com/oauth/oidc/callback

# OAuth Feature Flags
ENABLE_OAUTH_SIGNUP=true
OAUTH_MERGE_ACCOUNTS_BY_EMAIL=true

# =============================================================================
# USER MANAGEMENT CONFIGURATION
# =============================================================================
DEFAULT_USER_ROLE=user
ENABLE_ADMIN_EXPORT=true
ENABLE_ADMIN_CHAT_ACCESS=true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
ENABLE_COMMUNITY_SHARING=false
ENABLE_MESSAGE_RATING=true
ENABLE_MODEL_FILTER=true

# =============================================================================
# AI AND RAG FEATURES CONFIGURATION
# =============================================================================
DEFAULT_MODELS=llama2:latest
ENABLE_IMAGE_GENERATION=true
ENABLE_RAG_WEB_SEARCH=true
ENABLE_RAG_LOCAL_WEB_FETCH=true
RAG_WEB_SEARCH_ENGINE=searxng
RAG_EMBEDDING_MODEL=nomic-embed-text:latest

# =============================================================================
# OLLAMA CONFIGURATION
# =============================================================================
OLLAMA_KEEP_ALIVE=5m
OLLAMA_MAX_LOADED_MODELS=3

# =============================================================================
# PIPELINE CONFIGURATION
# =============================================================================
PIPELINES_URLS=https://github.com/open-webui/pipelines
ENABLE_OPENAI_API=true
ENABLE_OLLAMA_API=true

# =============================================================================
# SECURITY GENERATION COMMANDS
# =============================================================================
# Use these commands to generate secure random values:
#
# For passwords (32 characters):
# openssl rand -base64 32
#
# For secrets (64 characters):
# openssl rand -base64 64
#
# For hex values (32 characters):
# openssl rand -hex 16
#
# Example generation script:
# echo "POSTGRES_PASSWORD=$(openssl rand -base64 32)"
# echo "KEYCLOAK_DB_PASSWORD=$(openssl rand -base64 32)"
# echo "WEBUI_SECRET_KEY=$(openssl rand -base64 64)"
# echo "OAUTH_CLIENT_SECRET=$(openssl rand -base64 32)"

# =============================================================================
# DEPLOYMENT CHECKLIST
# =============================================================================
# Before deploying to production:
#
# 1. SECURITY:
#    □ Copy this file to .env
#    □ Generate secure random values for ALL passwords and secrets
#    □ Update all domain names to match your actual domains
#    □ Review and customize all feature flags
#    □ Ensure firewall rules are properly configured
#
# 2. INFRASTRUCTURE:
#    □ Verify NVIDIA Docker runtime is installed
#    □ Ensure sufficient disk space for volumes
#    □ Configure SSL certificates through Nginx Proxy Manager
#    □ Set up proper backup strategies for databases
#
# 3. KEYCLOAK SETUP:
#    □ Import realm configuration
#    □ Configure client settings
#    □ Set up user roles and permissions
#    □ Test OAuth authentication flow
#
# 4. MONITORING:
#    □ Verify all health checks are passing
#    □ Set up log aggregation
#    □ Configure alerting for service failures
#    □ Test disaster recovery procedures
#
# 5. PERFORMANCE:
#    □ Monitor GPU utilization
#    □ Optimize database performance
#    □ Configure resource limits appropriately
#    □ Test under expected load
#
# =============================================================================
# SUPPORT AND DOCUMENTATION
# =============================================================================
# For additional configuration options and troubleshooting:
# - OpenWebUI Documentation: https://docs.openwebui.com
# - Keycloak Documentation: https://www.keycloak.org/documentation
# - Ollama Documentation: https://ollama.ai/docs
# - Docker Compose Documentation: https://docs.docker.com/compose/
#
# =============================================================================
