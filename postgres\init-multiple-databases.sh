#!/bin/bash
set -e

# Production-Ready Multi-Database Initialization Script
# Creates separate databases for Keycloak and OpenWebUI with proper security

echo "Starting multi-database initialization..."

# Function to create database and user if they don't exist
create_database_and_user() {
    local db_name=$1
    local db_user=$2
    local db_password=$3

    echo "Creating database: $db_name with user: $db_user"

    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        -- Create database if it doesn't exist
        SELECT 'CREATE DATABASE $db_name'
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$db_name')\gexec

        -- Create user if it doesn't exist
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$db_user') THEN
                CREATE USER $db_user WITH ENCRYPTED PASSWORD '$db_password';
            END IF;
        END
        \$\$;

        -- <PERSON> privileges
        GRANT ALL PRIVILEGES ON DATABASE $db_name TO $db_user;

        -- Connect to the database and set up permissions
        \c $db_name;
        GRANT ALL ON SCHEMA public TO $db_user;
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $db_user;
        GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $db_user;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $db_user;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $db_user;

        -- Switch back to default database
        \c $POSTGRES_DB;
EOSQL
}

# Create OpenWebUI database and user
if [ -n "${OPENWEBUI_DB_NAME}" ] && [ -n "${OPENWEBUI_DB_USER}" ] && [ -n "${OPENWEBUI_DB_PASSWORD}" ]; then
    create_database_and_user "${OPENWEBUI_DB_NAME}" "${OPENWEBUI_DB_USER}" "${OPENWEBUI_DB_PASSWORD}"
else
    echo "OpenWebUI database variables not set, skipping OpenWebUI database creation"
fi

# Create Keycloak database and user
if [ -n "${KEYCLOAK_DB_NAME}" ] && [ -n "${KEYCLOAK_DB_USER}" ] && [ -n "${KEYCLOAK_DB_PASSWORD}" ]; then
    create_database_and_user "${KEYCLOAK_DB_NAME}" "${KEYCLOAK_DB_USER}" "${KEYCLOAK_DB_PASSWORD}"
else
    echo "Keycloak database variables not set, skipping Keycloak database creation"
fi

# Additional security configurations
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Revoke public schema permissions from public role for security
    REVOKE CREATE ON SCHEMA public FROM PUBLIC;

    -- Set connection limits for application users
    ALTER USER ${OPENWEBUI_DB_USER:-openwebui} CONNECTION LIMIT 50;
    ALTER USER ${KEYCLOAK_DB_USER:-keycloak} CONNECTION LIMIT 50;
EOSQL

echo "Multi-database initialization completed successfully!"
echo "Databases created:"
echo "  - ${OPENWEBUI_DB_NAME:-openwebui} (user: ${OPENWEBUI_DB_USER:-openwebui})"
echo "  - ${KEYCLOAK_DB_NAME:-keycloak} (user: ${KEYCLOAK_DB_USER:-keycloak})"
